{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@types/lodash": "^4.17.17", "vite": "^6.3.5"}, "dependencies": {"@headlessui/react": "^2.2.4", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "uuid": "^11.1.0"}}