import React, { useRef, useCallback } from 'react'
import { useDrop } from 'react-dnd'
import { TemplateComponent } from '../../types/templateEditor'
import { DraggableComponent } from './DraggableComponent'
import { SelectionBox } from './SelectionBox'
import { GridOverlay } from './GridOverlay'
import { ZoomControls } from './ZoomControls'

interface TemplateEditorCanvasProps {
  components: TemplateComponent[]
  selectedComponent: string | null
  onComponentSelect: (id: string | null) => void
  onComponentUpdate: (id: string, updates: Partial<TemplateComponent>) => void
  onComponentAdd: (component: TemplateComponent) => void
  onComponentDelete: (id: string) => void
  onComponentMove?: (fromIndex: number, toIndex: number) => void
  onComponentDuplicate?: (id: string) => void
  zoom: number
  onZoomChange?: (zoom: number) => void
  showGrid: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
  templateSettings?: any
}

export const TemplateEditorCanvas: React.FC<TemplateEditorCanvasProps> = ({
  components,
  selectedComponent,
  onComponentSelect,
  onComponentUpdate,
  onComponentAdd,
  onComponentDelete,
  onComponentMove: _onComponentMove,
  onComponentDuplicate: _onComponentDuplicate,
  zoom,
  onZoomChange,
  showGrid,
  previewMode,
  templateSettings: _templateSettings
}) => {
  const canvasRef = useRef<HTMLDivElement>(null)

  const [{ isOver }, drop] = useDrop({
    accept: ['component', 'existing-component'],
    drop: (item: any, monitor) => {
      if (!canvasRef.current) return

      const canvasRect = canvasRef.current.getBoundingClientRect()
      const offset = monitor.getClientOffset()
      
      if (offset) {
        const x = (offset.x - canvasRect.left) / zoom
        const y = (offset.y - canvasRect.top) / zoom

        if (item.type === 'component') {
          // Adding new component from library
          const newComponent: TemplateComponent = {
            id: `component-${Date.now()}`,
            type: item.componentType,
            content: item.defaultContent || {},
            styles: {
              ...item.defaultStyles,
              position: 'absolute',
              left: x,
              top: y,
              zIndex: components.length + 1
            },
            position: { x, y }
          }
          onComponentAdd(newComponent)
        } else if (item.type === 'existing-component') {
          // Moving existing component
          onComponentUpdate(item.id, {
            position: { x, y },
            styles: {
              ...item.styles,
              left: x,
              top: y
            }
          })
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  })

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      onComponentSelect(null)
    }
  }, [onComponentSelect])

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (selectedComponent) {
      switch (e.key) {
        case 'Delete':
        case 'Backspace':
          onComponentDelete(selectedComponent)
          break
        case 'ArrowUp':
          e.preventDefault()
          moveComponent(0, -1)
          break
        case 'ArrowDown':
          e.preventDefault()
          moveComponent(0, 1)
          break
        case 'ArrowLeft':
          e.preventDefault()
          moveComponent(-1, 0)
          break
        case 'ArrowRight':
          e.preventDefault()
          moveComponent(1, 0)
          break
      }
    }
  }, [selectedComponent])

  const moveComponent = (deltaX: number, deltaY: number) => {
    if (!selectedComponent) return
    
    const component = components.find(c => c.id === selectedComponent)
    if (!component) return

    const newX = (component.position?.x || 0) + deltaX
    const newY = (component.position?.y || 0) + deltaY

    onComponentUpdate(selectedComponent, {
      position: { x: newX, y: newY },
      styles: {
        ...component.styles,
        left: newX,
        top: newY
      }
    })
  }

  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  const getCanvasWidth = () => {
    switch (previewMode) {
      case 'mobile': return 375
      case 'tablet': return 768
      case 'desktop': return 600
      default: return 600
    }
  }

  drop(canvasRef)

  return (
    <div className="relative flex-1 overflow-auto bg-gray-100 p-8">
      {onZoomChange && <ZoomControls zoom={zoom} onZoomChange={onZoomChange} />}
      
      <div 
        className="relative mx-auto bg-white shadow-lg"
        style={{ 
          width: getCanvasWidth(),
          minHeight: 800,
          transform: `scale(${zoom})`,
          transformOrigin: 'top center'
        }}
      >
        <div
          ref={canvasRef}
          className={`relative w-full h-full ${isOver ? 'bg-blue-50' : ''}`}
          onClick={handleCanvasClick}
          style={{ minHeight: 800 }}
        >
          {showGrid && <GridOverlay />}
          
          {components.map((component) => (
            <DraggableComponent
              key={component.id}
              component={component}
              isSelected={selectedComponent === component.id}
              onSelect={() => onComponentSelect(component.id)}
              onUpdate={(updates) => onComponentUpdate(component.id, updates)}
              onDelete={onComponentDelete}
              zoom={zoom}
            />
          ))}

          {selectedComponent && (
            <SelectionBox
              component={components.find(c => c.id === selectedComponent)!}
              zoom={zoom}
            />
          )}
        </div>
      </div>
    </div>
  )
}
