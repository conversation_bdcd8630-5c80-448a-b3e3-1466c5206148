export interface TemplateComponent {
  id: string
  type: 'text' | 'heading' | 'image' | 'button' | 'divider' | 'spacer' | 'container' | 'header' | 'footer' | 'product-card' | 'social-media'
  content: Record<string, any>
  styles: Record<string, any>
  position: { x: number; y: number }
  children?: TemplateComponent[]
}

export interface TemplateEditorState {
  components: TemplateComponent[]
  selectedComponent: string | null
  history: TemplateComponent[][]
  historyIndex: number
  zoom: number
  showGrid: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
  isDirty: boolean
}

export interface TemplateEditorAction {
  type: 'ADD_COMPONENT' | 'UPDATE_COMPONENT' | 'DELETE_COMPONENT' | 'SELECT_COMPONENT' |
        'SET_ZOOM' | 'TOGGLE_GRID' | 'SET_PREVIEW_MODE' | 'UNDO' | 'REDO' | 'SAVE_STATE' |
        'RESET_COMPONENTS' | 'LOAD_COMPONENTS' | 'SYNC_FROM_HTML' | 'RESET_DIRTY' | 'LOAD_TEMPLATE'
  payload?: any
}

export interface ComponentDefinition {
  id: string
  name: string
  type: string
  icon: React.ReactNode
  defaultContent: Record<string, any>
  defaultStyles: Record<string, any>
  category: string
}

export interface ColorPalette {
  id: string
  name: string
  colors: string[]
}

export interface FontOption {
  family: string
  name: string
  weights: string[]
  category: 'serif' | 'sans-serif' | 'monospace' | 'display'
}

export interface TemplateSection {
  id: string
  name: string
  components: TemplateComponent[]
  styles: Record<string, any>
}

export interface ResponsiveStyles {
  desktop: Record<string, any>
  tablet: Record<string, any>
  mobile: Record<string, any>
}

export interface TemplateSettings {
  name: string
  description: string
  width: number
  backgroundColor: string
  fontFamily: string
  preheaderText: string
  subject: string
}

export interface UndoRedoState {
  past: TemplateComponent[][]
  present: TemplateComponent[]
  future: TemplateComponent[][]
}

export interface DragItem {
  type: 'component' | 'existing-component'
  componentType?: string
  id?: string
  defaultContent?: Record<string, any>
  defaultStyles?: Record<string, any>
}

export interface SelectionBounds {
  x: number
  y: number
  width: number
  height: number
}

export interface GridSettings {
  enabled: boolean
  size: number
  snapToGrid: boolean
  showGuides: boolean
}

export interface PreviewSettings {
  mode: 'desktop' | 'tablet' | 'mobile'
  darkMode: boolean
  emailClient: 'gmail' | 'outlook' | 'apple-mail' | 'generic'
}

export interface ExportOptions {
  format: 'html' | 'mjml' | 'amp'
  inlineCSS: boolean
  minify: boolean
  includePreheader: boolean
}

export interface TemplateValidation {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  componentId: string
  type: 'missing-content' | 'invalid-style' | 'accessibility' | 'compatibility'
  message: string
  severity: 'error' | 'warning'
}

export interface ValidationWarning {
  componentId: string
  type: 'performance' | 'best-practice' | 'accessibility'
  message: string
  suggestion: string
}

export interface TemplateTheme {
  id: string
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
  }
  fonts: {
    heading: string
    body: string
  }
  spacing: {
    small: string
    medium: string
    large: string
  }
}

export interface ComponentLibraryItem {
  id: string
  name: string
  description: string
  thumbnail: string
  category: string
  tags: string[]
  component: TemplateComponent
  isPremium: boolean
}

export interface TemplateHistory {
  id: string
  timestamp: Date
  action: string
  componentId?: string
  changes: Record<string, any>
}

export interface CollaborationState {
  isCollaborating: boolean
  collaborators: Collaborator[]
  cursors: CursorPosition[]
  selections: CollaboratorSelection[]
}

export interface Collaborator {
  id: string
  name: string
  avatar: string
  color: string
  isActive: boolean
}

export interface CursorPosition {
  collaboratorId: string
  x: number
  y: number
  timestamp: Date
}

export interface CollaboratorSelection {
  collaboratorId: string
  componentId: string
  timestamp: Date
}
